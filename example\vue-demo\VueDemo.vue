<template>
  <div class="demo-container">
    <h1 class="demo-title">LiteOFD Vue 组件演示</h1>
    
    <!-- 基础使用演示 -->
    <div class="demo-section">
      <h2 class="section-title">基础使用</h2>
      <OfdViewer 
        ref="basicViewer"
        :show-toolbar="true"
        @loaded="onDocumentLoaded"
        @error="onError"
        @page-change="onPageChange"
        @signature-click="onSignatureClick"
      />
    </div>
    
    <!-- 自定义控制演示 -->
    <div class="demo-section">
      <h2 class="section-title">自定义控制</h2>
      <div style="margin-bottom: 15px;">
        <button @click="loadSampleFile" class="demo-btn">加载示例文件</button>
        <button @click="jumpToPage" class="demo-btn">跳转到第3页</button>
        <button @click="searchText" class="demo-btn">搜索"示例"</button>
        <button @click="customZoom" class="demo-btn">设置150%缩放</button>
      </div>
      
      <OfdViewer 
        ref="customViewer"
        :show-toolbar="false"
        :scale="1.2"
        @loaded="onCustomLoaded"
      />
    </div>
    
    <!-- 无工具栏演示 -->
    <div class="demo-section">
      <h2 class="section-title">无工具栏模式</h2>
      <OfdViewer 
        :show-toolbar="false"
        :auto-load="false"
      />
    </div>
    
    <!-- 状态显示 -->
    <div class="demo-section">
      <h2 class="section-title">状态信息</h2>
      <div class="status-info">
        <p><strong>当前页面:</strong> {{ currentPage }} / {{ totalPages }}</p>
        <p><strong>缩放比例:</strong> {{ currentScale }}%</p>
        <p><strong>加载状态:</strong> {{ loadingStatus }}</p>
        <p v-if="errorMessage"><strong>错误信息:</strong> {{ errorMessage }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import OfdViewer from '../../src/ofdViewer/ofdViewer.vue'
import type { OfdDocument } from '../../src/liteofd/ofdDocument'

// 引用
const basicViewer = ref()
const customViewer = ref()

// 状态
const currentPage = ref(1)
const totalPages = ref(0)
const currentScale = ref(100)
const loadingStatus = ref('未加载')
const errorMessage = ref('')

// 事件处理
const onDocumentLoaded = (document: OfdDocument) => {
  console.log('文档加载完成:', document)
  loadingStatus.value = '加载完成'
  totalPages.value = basicViewer.value?.getTotalPages() || 0
  currentPage.value = 1
  errorMessage.value = ''
}

const onError = (error: Error) => {
  console.error('加载错误:', error)
  loadingStatus.value = '加载失败'
  errorMessage.value = error.message
}

const onPageChange = (page: number) => {
  console.log('页面变化:', page)
  currentPage.value = page
}

const onSignatureClick = (data: any) => {
  console.log('签名点击:', data)
  alert('签名被点击，详情请查看控制台')
}

const onCustomLoaded = (document: OfdDocument) => {
  console.log('自定义查看器文档加载完成:', document)
}

// 自定义控制方法
const loadSampleFile = () => {
  // 这里可以加载一个示例文件
  console.log('加载示例文件')
  loadingStatus.value = '加载中...'
}

const jumpToPage = () => {
  basicViewer.value?.goToPage(3)
}

const searchText = () => {
  basicViewer.value?.search('示例')
}

const customZoom = () => {
  basicViewer.value?.setZoom(1.5)
  currentScale.value = 150
}
</script>

<style scoped>
.demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.demo-title {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.demo-section {
  margin-bottom: 40px;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  background: #fafafa;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #555;
}

.demo-btn {
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.demo-btn:hover {
  background: #0056b3;
}

.status-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #007bff;
}

.status-info p {
  margin: 5px 0;
}
</style>