import { defineConfig } from "vite";
import { resolve } from "path";
import dts from "vite-plugin-dts";
import vue from "@vitejs/plugin-vue"; // 新增Vue插件

export default defineConfig(({ mode }) => {
  // 根据模式决定是否为库构建
  const isLib = mode === "lib";

  const baseConfig = {
    base: "./",

    // 服务器选项
    server: {
      host: "0.0.0.0",
      port: 3000,
      open: true,
      cors: true,
    },

    // 解析选项
    resolve: {
      alias: {
        "@": resolve(__dirname, "src"),
      },
    },

    publicDir: "public",
  };

  if (isLib) {
    // 库构建模式（原有配置）
    return {
      ...baseConfig,
      build: {
        lib: {
          entry: resolve(__dirname, "src/index.ts"),
          name: "liteofd",
          formats: ["es", "cjs"],
          fileName: (format) => {
            if (format === "es") {
              return "index.mjs";
            } else {
              return "index.js";
            }
          },
        },
        outDir: "dist",
        assetsDir: "assets",
        minify: "terser",
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true,
          },
        },
        rollupOptions: {
          external: [
            "vue",
            "fast-xml-parser",
            "js-md5",
            "js-sha1",
            "jsrsasign",
            "jsrsasign-util",
            "jszip",
            "jszip-utils",
            "sm-crypto",
            "xmlbuilder2",
          ],
          output: {
            globals: {
              vue: "Vue",
              "fast-xml-parser": "fastXmlParser",
              "js-md5": "md5",
              "js-sha1": "sha1",
              jsrsasign: "jsrsasign",
              "jsrsasign-util": "jsrsasignUtil",
              jszip: "JSZip",
              "jszip-utils": "JSZipUtils",
              "sm-crypto": "smCrypto",
              xmlbuilder2: "xmlbuilder2",
            },
            assetFileNames: (assetInfo) => {
              if (
                assetInfo.name &&
                (assetInfo.name.endsWith(".ttf") ||
                  assetInfo.name.endsWith(".otf"))
              ) {
                return "assets/fonts/[name][extname]";
              }
              return "assets/[name]-[hash][extname]";
            },
          },
        },
        emptyOutDir: false,
      },
      plugins: [
        vue(), // 添加Vue插件
        dts({
          rollupTypes: true,
          insertTypesEntry: true,
          outDir: "dist",
          include: ["src/**/*.ts"],
          exclude: ["src/**/*.spec.ts", "src/**/*.test.ts"],
        }),
      ],
    };
  } else {
    // 开发模式（支持Vue演示页面）
    return {
      ...baseConfig,

      plugins: [
        vue(), // Vue插件
      ],
      build: {
        rollupOptions: {
          input: {
            main: resolve(__dirname, "index.html"),
            vue: resolve(__dirname, "vue-demo.html"),
          },
        },
      },
    };
  }
});
