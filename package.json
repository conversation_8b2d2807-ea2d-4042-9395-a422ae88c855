{"name": "liteofd", "private": false, "version": "0.3.0", "description": "A lightweight OFD parsing and rendering library", "keywords": ["liteofd.js", "ofd", "liteofd", "ofd render", "ofd viewer", "ofd 渲染", "ofd 预览", "ofd 查看器", "pdf", "pdf 渲染", "pdf 预览", "pdf 查看器"], "type": "module", "module": "dist/index.js", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"require": "./dist/index.js", "import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "repository": {"type": "git", "url": "https://github.com/SignitDoc/liteofd"}, "homepage": "https://github.com/SignitDoc/liteofd", "author": "xxss0903", "license": "MIT", "scripts": {"dev": "vite", "build-vite": "vite build --mode production", "preview": "vite preview", "build": "webpack --mode production --config webpack.config.cjs"}, "dependencies": {"fast-xml-parser": "^4.4.1", "js-md5": "^0.7.3", "js-sha1": "^0.6.0", "jsrsasign": "^9.1.9", "jsrsasign-util": "^1.0.0", "jszip": "^3.5.0", "jszip-utils": "^0.1.0", "sm-crypto": "^0.2.1", "xmlbuilder2": "^3.1.1"}, "devDependencies": {"@types/node": "^22.5.2", "copy-webpack-plugin": "^12.0.2", "css-loader": "^7.1.2", "file-loader": "^6.2.0", "style-loader": "^4.0.0", "terser": "^5.31.6", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "vite": "^5.4.2", "vite-plugin-dts": "^4.2.4", "webpack": "^5.94.0", "webpack-cli": "^5.1.4"}, "files": ["dist"]}