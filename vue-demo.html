<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="public/assets/logo.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>LiteOFD Vue 演示</title>
  <style>
    @font-face {
      font-family: 'Times-Bold';
      src: url('public/assets/fonts/Times-Bold.otf');
    }
    @font-face {
      font-family: 'NSimSun';
      src: url('public/assets/fonts/Nsimsun.ttf');
    }
    @font-face {
      font-family: 'FangSong_GB2312';
      src: url('public/assets/fonts/FangSong_GB2312.otf');
    }
    @font-face {
      font-family: 'SimFang';
      src: url('public/assets/fonts/SIMFANG.TTF');
    }
    @font-face {
      font-family: 'ArialMT';
      src: url('public/assets/fonts/ArialMT.ttf');
    }
    
    body {
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
    }
    
    .demo-container {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .demo-title {
      text-align: center;
      color: #333;
      margin-bottom: 30px;
    }
    
    .demo-section {
      margin-bottom: 40px;
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
    }
    
    .section-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #555;
    }
  </style>
</head>
<body>
  <div id="app"></div>
  <script type="module" src="/example/vue-demo/main.ts"></script>
</body>
</html>